import React, { useState, useEffect } from "react";
import { saveAs } from 'file-saver';
import { Link } from 'react-router-dom';
import "./data.css"

function Report() {
  const [data, setData] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  const [userRole, setUserRole] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedYear, setSelectedYear] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [sortField, setSortField] = useState('package');
  const [sortDirection, setSortDirection] = useState('desc');
  const [availableYears, setAvailableYears] = useState([]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showExportDropdown && !event.target.closest('.export-dropdown-container')) {
        setShowExportDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showExportDropdown]);

  // Function to fetch placement data with optional year filter
  const fetchPlacementData = async (year = '') => {
    try {
      setLoading(true);
      setError('');

      // Get user token from localStorage
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      if (!userInfo || !userInfo.token) {
        throw new Error('You must be logged in to view this data');
      }

      // Set user role
      setUserRole(userInfo.role);

      // Build URL with query parameters
      let url = 'http://localhost:5000/api/placement-data';
      if (year) {
        url += `?year=${year}`;
      }

      // Fetch data from backend API
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${userInfo.token}`
        }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch placement data');
      }

      setData(result);

      // Extract available years from the data
      const years = new Set();
      result.forEach(item => {
        if (item.yearPlaced) {
          const year = new Date(item.yearPlaced).getFullYear();
          if (!isNaN(year)) {
            years.add(year);
          }
        }
      });

      // Sort years in descending order (newest first)
      setAvailableYears(Array.from(years).sort((a, b) => b - a));
    } catch (error) {
      setError(error.message);
      // Fallback to local storage if API fails
      const details = localStorage.getItem('formData');
      if (details) {
        setData(JSON.parse(details));
      }
    } finally {
      setLoading(false);
    }
  };

  // Initial data load
  useEffect(() => {
    fetchPlacementData();
  }, []);

  // Fetch data when year filter changes
  useEffect(() => {
    // Only fetch if selectedYear has been initialized (not on first render)
    if (selectedYear !== undefined) {
      fetchPlacementData(selectedYear);
    }
  }, [selectedYear]);


  const handleRowClick = (student) => {
    setSelectedStudent(student);
  };

  const handleCloseDetails = () => {
    setSelectedStudent(null);
  };

  // Filter and sort data
  const filteredData = data.filter(student => {
    const fullName = `${student.firstname} ${student.lastname}`.toLowerCase();
    const rollNo = student.rollno.toLowerCase();
    const company = student.companyPlaced.toLowerCase();
    const branch = student.branch.toLowerCase();
    const search = searchTerm.toLowerCase();

    // Format the year placed for display and search
    let yearPlaced = '';
    if (student.yearPlaced) {
      const date = new Date(student.yearPlaced);
      yearPlaced = date.getFullYear().toString();
    }

    return (
      fullName.includes(search) ||
      rollNo.includes(search) ||
      company.includes(search) ||
      branch.includes(search) ||
      yearPlaced.includes(search)
    );
  });

  // Sort data
  const sortedData = [...filteredData].sort((a, b) => {
    if (sortField === 'name') {
      const nameA = `${a.firstname} ${a.lastname}`.toLowerCase();
      const nameB = `${b.firstname} ${b.lastname}`.toLowerCase();
      return sortDirection === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
    } else if (sortField === 'package') {
      return sortDirection === 'asc' ? a.package - b.package : b.package - a.package;
    } else if (sortField === 'year') {
      const yearA = a.yearPlaced ? new Date(a.yearPlaced).getFullYear() : 0;
      const yearB = b.yearPlaced ? new Date(b.yearPlaced).getFullYear() : 0;
      return sortDirection === 'asc' ? yearA - yearB : yearB - yearA;
    }
    return 0;
  });

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedData.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Pagination controls
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Export functions
  const exportToCSV = () => {
    try {
      // Create CSV content
      const headers = ['Sl.No', 'Roll No', 'Name', 'Branch', 'CGPA', 'Company', 'Package (LPA)', 'Status', 'Year Placed'];

      // Helper function to escape CSV values
      const escapeCSV = (value) => {
        if (value == null) return '';
        const stringValue = String(value);
        // If the value contains commas, quotes, or newlines, wrap it in quotes and escape any quotes
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      };

      const csvContent = sortedData.map((student, index) => {
        // Format the year placed for export
        let yearPlaced = '';
        if (student.yearPlaced) {
          const date = new Date(student.yearPlaced);
          yearPlaced = date.getFullYear().toString();
        }

        return [
          escapeCSV(index + 1),
          escapeCSV(student.rollno),
          escapeCSV(`${student.firstname} ${student.lastname}`),
          escapeCSV(student.branch),
          escapeCSV(student.cgpa),
          escapeCSV(student.companyPlaced),
          escapeCSV(student.package),
          escapeCSV(student.status),
          escapeCSV(yearPlaced)
        ].join(',');
      });

      // Add headers to the beginning
      csvContent.unshift(headers.join(','));

      // Create blob and download
      const blob = new Blob([csvContent.join('\n')], { type: 'text/csv;charset=utf-8' });
      saveAs(blob, `placement_data_${new Date().toISOString().slice(0, 10)}.csv`);
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      alert('Failed to export data. Please try again.');
    }
  };

  const exportToExcel = () => {
    try {
      // Create a simple Excel-compatible CSV
      const headers = ['Sl.No', 'Roll No', 'Name', 'Branch', 'CGPA', 'Company', 'Package (LPA)', 'Status', 'Year Placed'];

      // Helper function to escape Excel values
      const escapeExcel = (value) => {
        if (value == null) return '';
        const stringValue = String(value);
        // If the value contains tabs, quotes, or newlines, wrap it in quotes and escape any quotes
        if (stringValue.includes('\t') || stringValue.includes('"') || stringValue.includes('\n')) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      };

      const excelContent = sortedData.map((student, index) => {
        // Format the year placed for export
        let yearPlaced = '';
        if (student.yearPlaced) {
          const date = new Date(student.yearPlaced);
          yearPlaced = date.getFullYear().toString();
        }

        return [
          escapeExcel(index + 1),
          escapeExcel(student.rollno),
          escapeExcel(`${student.firstname} ${student.lastname}`),
          escapeExcel(student.branch),
          escapeExcel(student.cgpa),
          escapeExcel(student.companyPlaced),
          escapeExcel(student.package),
          escapeExcel(student.status),
          escapeExcel(yearPlaced)
        ].join('\t'); // Tab-separated for Excel
      });

      // Add headers to the beginning
      excelContent.unshift(headers.join('\t'));

      // Create blob and download
      const blob = new Blob([excelContent.join('\n')], { type: 'application/vnd.ms-excel;charset=utf-8' });
      saveAs(blob, `placement_data_${new Date().toISOString().slice(0, 10)}.xls`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      alert('Failed to export data. Please try again.');
    }
  };

  // Handle export dropdown
  const handleExport = (format) => {
    if (format === 'csv') {
      exportToCSV();
    } else if (format === 'excel') {
      exportToExcel();
    }
  };

  return (
    <div className="reportContainer">
      <div className="report-header">
        <div>
          <h1 className="report-title">Placement Data</h1>
          <p className="report-subtitle">View and explore student placement information</p>
        </div>
        <div className="report-actions">
          {/* CSV Import Button - Only visible to faculty/admin */}
          {(userRole === 'faculty' || userRole === 'admin') && (
            <Link to="/import-csv" className="btn btn-primary me-2">
              <i className="bi bi-file-earmark-arrow-up me-2"></i>Import CSV
            </Link>
          )}

          <div className="export-dropdown-container">
            <button
              className="btn btn-outline-secondary"
              type="button"
              onClick={() => setShowExportDropdown(!showExportDropdown)}
              disabled={loading || data.length === 0}
            >
              <i className="bi bi-download me-2"></i>Export
            </button>
            {showExportDropdown && (
              <div className="custom-dropdown-menu">
                <button
                  className="custom-dropdown-item"
                  onClick={() => {
                    handleExport('csv');
                    setShowExportDropdown(false);
                  }}
                >
                  <i className="bi bi-filetype-csv me-2"></i>Export as CSV
                </button>
                <button
                  className="custom-dropdown-item"
                  onClick={() => {
                    handleExport('excel');
                    setShowExportDropdown(false);
                  }}
                >
                  <i className="bi bi-file-earmark-excel me-2"></i>Export as Excel
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {error && <div className="alert alert-danger mb-4">{error}</div>}

      {loading ? (
        <div className="text-center p-5">
          <div className="spinner-border" style={{ color: 'rgb(135, 4, 4)' }} role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading placement data...</p>
        </div>
      ) : data.length === 0 ? (
        <div className="alert alert-info">No placement data available.</div>
      ) : (
        <>
          <div className="search-filter-container">
            <div className="search-box">
              <i className="bi bi-search search-icon"></i>
              <input
                type="text"
                placeholder="Search by name, roll no, company, or year..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1); // Reset to first page on search
                }}
              />
            </div>

            {/* Year Filter Dropdown */}
            <select
              className="form-select filter-dropdown me-2"
              value={selectedYear}
              onChange={(e) => {
                setSelectedYear(e.target.value);
                setCurrentPage(1); // Reset to first page on year change
              }}
              style={{ maxWidth: '150px' }}
            >
              <option value="">All Years</option>
              {availableYears.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>

            {/* Sort Dropdown */}
            <select
              className="form-select filter-dropdown"
              value={`${sortField}-${sortDirection}`}
              onChange={(e) => {
                const [field, direction] = e.target.value.split('-');
                setSortField(field);
                setSortDirection(direction);
              }}
            >
              <option value="package-desc">Highest Package</option>
              <option value="package-asc">Lowest Package</option>
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
              <option value="year-desc">Latest Year First</option>
              <option value="year-asc">Earliest Year First</option>
            </select>
          </div>

          <table className="table table-hover">
            <thead>
              <tr>
                <th scope="col">Sl.No</th>
                <th scope="col">Roll No</th>
                <th scope="col" onClick={() => handleSort('name')} style={{ cursor: 'pointer' }}>
                  Name {sortField === 'name' && (
                    <i className={`bi bi-arrow-${sortDirection === 'asc' ? 'up' : 'down'}`}></i>
                  )}
                </th>
                <th scope="col">Company</th>
                <th scope="col" onClick={() => handleSort('package')} style={{ cursor: 'pointer' }}>
                  Package (LPA) {sortField === 'package' && (
                    <i className={`bi bi-arrow-${sortDirection === 'asc' ? 'up' : 'down'}`}></i>
                  )}
                </th>
                <th scope="col" onClick={() => handleSort('year')} style={{ cursor: 'pointer' }}>
                  Year Placed {sortField === 'year' && (
                    <i className={`bi bi-arrow-${sortDirection === 'asc' ? 'up' : 'down'}`}></i>
                  )}
                </th>
              </tr>
            </thead>
            <tbody>
              {currentItems.map((student, index) => {
                // Format the year placed for display
                let yearPlaced = '';
                if (student.yearPlaced) {
                  const date = new Date(student.yearPlaced);
                  yearPlaced = date.getFullYear();
                }

                return (
                  <tr key={index} onClick={() => handleRowClick(student)}>
                    <td>{indexOfFirstItem + index + 1}</td>
                    <td>{student.rollno}</td>
                    <td>{student.firstname} {student.lastname}</td>
                    <td>
                      <span className="company-badge">{student.companyPlaced}</span>
                    </td>
                    <td className="package-cell">₹{student.package}</td>
                    <td>{yearPlaced}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="pagination-container">
              <button
                className="pagination-button"
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <i className="bi bi-chevron-left"></i>
              </button>

              {[...Array(totalPages).keys()].map(number => (
                <button
                  key={number + 1}
                  onClick={() => paginate(number + 1)}
                  className={`pagination-button ${currentPage === number + 1 ? 'active' : ''}`}
                >
                  {number + 1}
                </button>
              ))}

              <button
                className="pagination-button"
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <i className="bi bi-chevron-right"></i>
              </button>
            </div>
          )}
        </>
      )}

      {selectedStudent && (
        <div className="modalBackdrop" onClick={handleCloseDetails}>
          <div className="modalContent" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Student Profile</h2>
              <button className="close" onClick={handleCloseDetails}>
                <i className="bi bi-x"></i>
              </button>
            </div>

            <div className="modal-body">
              <div className="student-detail-grid">
                <div className="detail-item">
                  <span className="detail-label">Full Name</span>
                  <div className="detail-value">{selectedStudent.firstname} {selectedStudent.lastname}</div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Roll Number</span>
                  <div className="detail-value">{selectedStudent.rollno}</div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Branch</span>
                  <div className="detail-value">{selectedStudent.branch.toUpperCase()}</div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">CGPA</span>
                  <div className="detail-value">{selectedStudent.cgpa}</div>
                </div>
              </div>

              <hr />

              <h4 className="mb-3">Placement Information</h4>
              <div className="student-detail-grid">
                <div className="detail-item">
                  <span className="detail-label">Company</span>
                  <div className="detail-value">
                    <span className="company-badge">{selectedStudent.companyPlaced}</span>
                  </div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Package</span>
                  <div className="detail-value detail-highlight">₹{selectedStudent.package} LPA</div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Status</span>
                  <div className="detail-value">
                    <span className={`status-badge ${selectedStudent.status === 'Placed' ? 'status-placed' :
                                      selectedStudent.status === 'Not Placed' ? 'status-not-placed' : 'status-intern'}`}>
                      {selectedStudent.status}
                    </span>
                  </div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Company Type</span>
                  <div className="detail-value">{selectedStudent.companyType}</div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Year Joined</span>
                  <div className="detail-value">
                    {selectedStudent.yearJoined ? new Date(selectedStudent.yearJoined).getFullYear() : ''}
                  </div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Year Placed</span>
                  <div className="detail-value">
                    {selectedStudent.yearPlaced ? new Date(selectedStudent.yearPlaced).getFullYear() : ''}
                  </div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Placement Type</span>
                  <div className="detail-value">{selectedStudent.campus}</div>
                </div>
              </div>

              <hr />

              <h4 className="mb-3">Contact Information</h4>
              <div className="student-detail-grid">
                <div className="detail-item">
                  <span className="detail-label">Primary Contact</span>
                  <div className="detail-value">{selectedStudent.mobilenumber1}</div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Secondary Contact</span>
                  <div className="detail-value">{selectedStudent.mobilenumber2 || 'N/A'}</div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">Personal Email</span>
                  <div className="detail-value">{selectedStudent.personalEmail}</div>
                </div>

                <div className="detail-item">
                  <span className="detail-label">College Email</span>
                  <div className="detail-value">{selectedStudent.collegeEmail}</div>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={handleCloseDetails}>Close</button>
            </div>
          </div>
        </div>
      )}

    </div>
  )
}

export default Report;
