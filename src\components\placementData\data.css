.reportContainer {
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  margin: 2rem auto;
  max-width: 1200px;
  width: 95%;
  overflow-x: auto;
}

.dark-mode .reportContainer {
  background-color: var(--dark-bg-secondary);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(135, 4, 4, 0.2);
}

.report-title {
  color: rgb(135, 4, 4);
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.report-subtitle {
  color: #666;
  font-size: 1rem;
  margin-top: 0.5rem;
}

.report-actions {
  display: flex;
  gap: 1rem;
}

/* Import button styles */
.report-actions .btn-primary {
  background-color: rgb(135, 4, 4);
  border-color: rgb(135, 4, 4);
  transition: all 0.3s ease;
}

.report-actions .btn-primary:hover,
.report-actions .btn-primary:focus {
  background-color: rgb(110, 3, 3);
  border-color: rgb(110, 3, 3);
}

.dark-mode .report-actions .btn-primary {
  background-color: var(--dark-accent);
  border-color: var(--dark-accent);
}

/* Export button styles */
.report-actions .btn-outline-secondary {
  color: rgb(135, 4, 4);
  border-color: rgb(135, 4, 4);
  transition: all 0.3s ease;
}

.report-actions .btn-outline-secondary:hover:not(:disabled) {
  background-color: rgb(135, 4, 4);
  color: white;
  border-color: rgb(135, 4, 4);
}

.report-actions .btn-outline-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Export dropdown styles */
.export-dropdown-container {
  position: relative;
}

.custom-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  min-width: 180px;
  margin-top: 0.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: white;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.2s ease;
}

.custom-dropdown-item {
  background: none;
  border: none;
  text-align: left;
  border-radius: 4px;
  padding: 0.6rem 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
  color: #212529;
}

.custom-dropdown-item:hover {
  background-color: rgba(135, 4, 4, 0.1);
  color: rgb(135, 4, 4);
}

.custom-dropdown-item i {
  color: rgb(135, 4, 4);
}

/* Dark mode export styles */
.dark-mode .report-actions .btn-outline-secondary {
  color: #ffffff;
  border-color: #ffffff;
}

.dark-mode .report-actions .btn-outline-secondary:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.dark-mode .custom-dropdown-menu {
  background-color: var(--dark-card-bg);
  box-shadow: 0 4px 12px var(--dark-shadow);
}

.dark-mode .custom-dropdown-item {
  color: #ffffff;
}

.dark-mode .custom-dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.dark-mode .custom-dropdown-item i {
  color: var(--dark-accent);
}

.table {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 2rem;
}

.table thead th {
  background-color: rgb(135, 4, 4) !important;
  color: white;
  font-weight: 600;
  padding: 1rem;
  border: none;
}

.table tbody tr {
  cursor: pointer;
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: rgba(135, 4, 4, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.table tbody td {
  padding: 1rem;
  vertical-align: middle;
}

/* Dark mode table styles */
.dark-mode .table {
  background-color: var(--dark-bg-secondary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  color: var(--dark-text-primary);
}

.dark-mode .table thead th {
  background-color: #222222 !important;
  color: white;
  border-color: var(--dark-border);
}

.dark-mode .table tbody tr {
  border-color: var(--dark-border);
}

.dark-mode .table tbody tr:hover {
  background-color: rgba(255, 82, 82, 0.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.dark-mode .table tbody td {
  border-color: var(--dark-border);
  color: var(--dark-text-primary);
}

.package-cell {
  font-weight: 600;
  color: rgb(135, 4, 4);
}

.dark-mode .package-cell {
  color: var(--dark-accent);
}

.status-badge {
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
}

.status-placed {
  background-color: rgba(25, 135, 84, 0.15);
  color: rgb(25, 135, 84);
}

.status-not-placed {
  background-color: rgba(220, 53, 69, 0.15);
  color: rgb(220, 53, 69);
}

.status-intern {
  background-color: rgba(13, 110, 253, 0.15);
  color: rgb(13, 110, 253);
}

.search-filter-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 250px;
  position: relative;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 50px;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: rgb(135, 4, 4);
  box-shadow: 0 0 0 3px rgba(135, 4, 4, 0.1);
}

.dark-mode .search-box input {
  background-color: var(--dark-input-bg);
  border-color: var(--dark-border);
  color: var(--dark-text-primary);
}

.dark-mode .search-box input:focus {
  border-color: var(--dark-accent);
  box-shadow: 0 0 0 3px rgba(255, 82, 82, 0.2);
}

.dark-mode .search-icon {
  color: var(--dark-text-secondary);
}

.dark-mode .filter-dropdown {
  background-color: var(--dark-input-bg);
  border-color: var(--dark-border);
  color: var(--dark-text-primary);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.filter-dropdown {
  min-width: 150px;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-dropdown:focus {
  outline: none;
  border-color: rgb(135, 4, 4);
  box-shadow: 0 0 0 3px rgba(135, 4, 4, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.pagination-button {
  background-color: white;
  border: 1px solid #ddd;
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .pagination-button {
  background-color: var(--dark-card-bg);
  border: 1px solid var(--dark-border);
  color: white;
}

.pagination-button:hover {
  background-color: #f8f9fa;
}

.dark-mode .pagination-button:hover {
  background-color: var(--dark-bg-secondary);
}

.pagination-button.active {
  background-color: rgb(135, 4, 4);
  color: white;
  border-color: rgb(135, 4, 4);
}

.dark-mode .pagination-button.active {
  background-color: var(--dark-accent);
  color: white;
  border-color: var(--dark-accent);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modalBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modalContent {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 700px;
  width: 90%;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  padding: 0;
  animation: slideUp 0.4s ease;
  display: flex;
  flex-direction: column;
}

.dark-mode .modalContent {
  background-color: var(--dark-card-bg);
  box-shadow: 0 10px 30px var(--dark-shadow);
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-header {
  background-color: rgb(135, 4, 4);
  color: white;
  padding: 1.5rem;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  position: relative;
}

.dark-mode .modal-header {
  background-color: var(--dark-accent);
  color: white;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.modal-body {
  padding: 2rem;
}

.student-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.detail-item {
  margin-bottom: 1rem;
}

.detail-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.25rem;
  display: block;
}

.dark-mode .detail-label {
  color: #cccccc;
}

.detail-value {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

.dark-mode .detail-value {
  color: #ffffff;
}

.detail-highlight {
  color: rgb(135, 4, 4);
  font-weight: 600;
}

.dark-mode .detail-highlight {
  color: var(--dark-accent);
  font-weight: 600;
}

.close {
  position: absolute;
  top: 1rem;
  right: 1.5rem;
  font-size: 1.8rem;
  color: white;
  cursor: pointer;
  opacity: 0.8;
  transition: all 0.2s ease;
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.company-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: rgba(135, 4, 4, 0.1);
  color: rgb(135, 4, 4);
  border-radius: 50px;
  font-weight: 600;
  margin-top: 0.5rem;
}

.dark-mode .company-badge {
  background-color: rgba(255, 82, 82, 0.2);
  color: var(--dark-accent);
}

.modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.dark-mode .modal-footer {
  border-top: 1px solid var(--dark-border);
}

/* Scrollbar styling */
.modalContent::-webkit-scrollbar {
  width: 8px;
}

.modalContent::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.dark-mode .modalContent::-webkit-scrollbar-track {
  background: #333333;
  border-radius: 10px;
}

.modalContent::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 10px;
}

.dark-mode .modalContent::-webkit-scrollbar-thumb {
  background: #555555;
  border-radius: 10px;
}

.modalContent::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

.dark-mode .modalContent::-webkit-scrollbar-thumb:hover {
  background: #777777;
}

/* Enhanced responsive styles */
@media (max-width: 992px) {
  .reportContainer {
    padding: 1.5rem;
  }

  .report-title {
    font-size: 1.8rem;
  }

  .table thead th,
  .table tbody td {
    padding: 0.75rem;
  }

  .student-detail-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .reportContainer {
    padding: 1rem;
    margin: 1rem auto;
  }

  .report-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .report-actions {
    width: 100%;
    justify-content: space-between;
  }

  .search-filter-container {
    flex-direction: column;
  }

  .search-box {
    width: 100%;
    margin-bottom: 1rem;
  }

  .filter-dropdown {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .table {
    display: block;
    width: 100%;
    overflow-x: auto;
  }

  .modalContent {
    width: 95%;
    max-height: 80vh;
  }

  .modal-header h2 {
    font-size: 1.5rem;
  }

  .student-detail-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .reportContainer {
    padding: 0.75rem;
    margin: 0.75rem auto;
  }

  .report-title {
    font-size: 1.5rem;
  }

  .report-subtitle {
    font-size: 0.9rem;
  }

  .table thead th,
  .table tbody td {
    padding: 0.5rem;
    font-size: 0.9rem;
  }

  .status-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .pagination-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .detail-label {
    font-size: 0.8rem;
  }

  .detail-value {
    font-size: 1rem;
  }
}